package com.cdkit.modules.cm.application.projectplan;

import com.cdkit.common.exception.CdkitCloudException;
import com.cdkit.common.page.PageReq;
import com.cdkit.common.page.PageRes;
import com.cdkit.common.workflow.WorkFlowUtil;
import com.cdkit.common.workflow.entity.WorkflowProcessEntity;
import com.cdkit.modules.cm.api.projectplan.dto.*;
import com.cdkit.modules.cm.api.projectplan.dto.CostProjectPlanCalculateRequest;
import com.cdkit.modules.cm.domain.projectplan.mode.entity.CostProjectPlanEntity;
import com.cdkit.modules.cm.domain.projectplan.mode.entity.CostProjectPlanDetailEntity;
import com.cdkit.modules.cm.domain.projectplan.mode.entity.CostDirectCostEntity;
import com.cdkit.modules.cm.domain.projectplan.mode.entity.CostOtherCostEntity;
import com.cdkit.modules.cm.domain.projectplan.mode.entity.CostTaxCostEntity;
import com.cdkit.modules.cm.domain.projectplan.mode.entity.CostMaterialDetailEntity;
import com.cdkit.modules.cm.domain.projectplan.repository.CostProjectPlanRepository;
import com.cdkit.modules.cm.domain.projectplan.service.CostProjectPlanDomainService;
import com.cdkit.modules.cm.domain.projectplan.valobj.MaterialSummaryVO;
import com.cdkit.modules.cm.application.plm.ProductFileApplication;
import com.cdkit.modules.cm.application.materialprice.MaterialPriceApplication;
import com.cdkit.modules.cm.domain.gateway.plm.entity.ProductRecipeInfo;
import com.cdkit.modules.cm.domain.gateway.plm.entity.ProductFileTreeEntity;
import com.cdkit.modules.cm.domain.materialprice.mode.entity.CostMaterialPriceEntity;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Arrays;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.stream.Collectors;
import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 项目计划应用服务
 *
 * <AUTHOR>
 * @date 2025/07/18
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class CostProjectPlanApplicationService {

    private final CostProjectPlanRepository costProjectPlanRepository;
    private final CostProjectPlanDomainService costProjectPlanDomainService;
    private final ProductFileApplication productFileApplication;
    private final MaterialPriceApplication materialPriceApplication;
    @Resource
    WorkFlowUtil workFlowUtil;

    /**
     * 分页查询项目计划列表（年度计划关联季度计划）
     *
     * @param queryDTO 查询条件
     * @param pageReq 分页条件
     * @return 查询结果
     */
    public PageRes<CostProjectPlanDTO> queryPageList(CostProjectPlanDTO queryDTO, PageReq pageReq) {
        // 转换查询条件
        CostProjectPlanEntity queryEntity = new CostProjectPlanEntity();
        BeanUtils.copyProperties(queryDTO, queryEntity);

        // 通过仓储查询年度计划（Repository已修改为只查询年度计划）
        PageRes<CostProjectPlanEntity> domainPageRes = costProjectPlanRepository.page(queryEntity, pageReq);

        // 转换结果并关联季度计划
        List<CostProjectPlanDTO> dtoList = null;
        if (domainPageRes.getRecords() != null) {
            dtoList = domainPageRes.getRecords().stream()
                .map(this::convertToDTOWithChildPlans)
                .collect(Collectors.toList());
        }

        return PageRes.of(domainPageRes.getCurrent(), domainPageRes.getSize(), dtoList,
            domainPageRes.getTotal(), domainPageRes.getPages());
    }

    /**
     * 新增项目计划
     *
     * @param request 新增请求
     * @return 项目计划ID
     */
    @Transactional(rollbackFor = Exception.class)
    public String add(CostProjectPlanAddRequest request) {
        // 参数校验
        validateProjectPlanRequest(request);

        // 转换为领域实体
        CostProjectPlanEntity projectPlan = convertToEntity(request);

        // 转换子表数据
        List<CostProjectPlanDetailEntity> detailList = convertDetailList(request.getDetailList());
        List<CostDirectCostEntity> directCostList = convertDirectCostList(request.getDirectCostList());
        List<CostOtherCostEntity> otherCostList = convertOtherCostList(request.getOtherCostList());
        List<CostTaxCostEntity> taxCostList = convertTaxCostList(request.getTaxCostList());
        List<CostMaterialDetailEntity> materialDetailList = convertMaterialDetailList(request.getMaterialDetailList());

        // 保存
        boolean success = costProjectPlanRepository.saveMain(projectPlan, detailList, directCostList, otherCostList, taxCostList, materialDetailList);

        if (!success) {
            throw new CdkitCloudException("保存项目计划失败");
        }

        return projectPlan.getId();
    }

    /**
     * 编辑项目计划
     *
     * @param request 编辑请求
     * @return 项目计划ID
     */
    @Transactional(rollbackFor = Exception.class)
    public String edit(CostProjectPlanEditRequest request) {
        // 参数校验
        if (!StringUtils.hasText(request.getId())) {
            throw new CdkitCloudException("ID不能为空");
        }

        // 查询原记录
        CostProjectPlanEntity existingEntity = costProjectPlanRepository.getDomainById(request.getId());
        if (existingEntity == null) {
            throw new CdkitCloudException("记录不存在");
        }

        // 状态校验
        if (!existingEntity.canEdit()) {
            throw new CdkitCloudException("当前状态不允许编辑");
        }

        validateProjectPlanRequest(request);

        // 转换为领域实体
        CostProjectPlanEntity projectPlan = convertToEntity(request);
        projectPlan.setId(request.getId());
        projectPlan.setPlanCode(existingEntity.getPlanCode()); // 保持原编号
        projectPlan.setProjectPlanStatus(existingEntity.getProjectPlanStatus()); // 保持原状态

        // 转换子表数据
        List<CostProjectPlanDetailEntity> detailList = convertDetailList(request.getDetailList());
        List<CostDirectCostEntity> directCostList = convertDirectCostList(request.getDirectCostList());
        List<CostOtherCostEntity> otherCostList = convertOtherCostList(request.getOtherCostList());
        List<CostTaxCostEntity> taxCostList = convertTaxCostList(request.getTaxCostList());
        List<CostMaterialDetailEntity> materialDetailList = convertMaterialDetailList(request.getMaterialDetailList());

        // 更新
        boolean success = costProjectPlanRepository.updateMain(projectPlan, detailList, directCostList, otherCostList, taxCostList, materialDetailList);

        if (!success) {
            throw new CdkitCloudException("更新项目计划失败");
        }

        return projectPlan.getId();
    }

    /**
     * 删除项目计划
     *
     * @param id 项目计划ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void delete(String id) {
        if (!StringUtils.hasText(id)) {
            throw new CdkitCloudException("ID不能为空");
        }

        // 查询记录是否存在
        CostProjectPlanEntity existingEntity = costProjectPlanRepository.getDomainById(id);
        if (existingEntity == null) {
            throw new CdkitCloudException("记录不存在");
        }

        // 状态校验
        if (!existingEntity.canEdit()) {
            throw new CdkitCloudException("当前状态不允许删除");
        }

        // 删除
        boolean success = costProjectPlanRepository.deleteMain(id);
        if (!success) {
            throw new CdkitCloudException("删除项目计划失败");
        }
    }

    /**
     * 批量删除项目计划
     *
     * @param ids 项目计划ID列表，逗号分隔
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteBatch(String ids) {
        if (!StringUtils.hasText(ids)) {
            throw new CdkitCloudException("ID列表不能为空");
        }

        String[] idArray = ids.split(",");
        List<String> idList = Arrays.asList(idArray);

        // 批量状态校验
        for (String id : idList) {
            CostProjectPlanEntity existingEntity = costProjectPlanRepository.getDomainById(id.trim());
            if (existingEntity != null && !existingEntity.canEdit()) {
                throw new CdkitCloudException("项目计划 " + existingEntity.getPlanName() + " 当前状态不允许删除");
            }
        }

        // 批量删除
        boolean success = costProjectPlanRepository.deleteBatchMain(idList);
        if (!success) {
            throw new CdkitCloudException("批量删除项目计划失败");
        }
    }



    /**
     * 根据ID查询项目计划详情
     *
     * @param id 项目计划ID
     * @return 项目计划详情
     */
    public CostProjectPlanDTO getById(String id) {
        if (!StringUtils.hasText(id)) {
            throw new CdkitCloudException("ID不能为空");
        }

        CostProjectPlanEntity entity = costProjectPlanRepository.queryByIdWithDetails(id);
        if (entity == null) {
            throw new CdkitCloudException("记录不存在");
        }

        return convertToDTO(entity);
    }

    /**
     * 根据传入的数据计算项目计划预算依据（用于新增时的计算）
     *
     * @param request 项目计划数据
     * @return 计算结果
     */
    public CostProjectPlanCalculateResponse calculateBudgetBasisByData(CostProjectPlanCalculateRequest request) {
        if (request == null) {
            throw new CdkitCloudException("请求参数不能为空");
        }

        try {
            // 转换为领域实体
            CostProjectPlanEntity projectPlan = convertCalculateRequestToEntity(request);

            // 设置子表数据
            projectPlan.setCostProjectPlanDetailList(convertDetailList(request.getDetailList()));
            projectPlan.setCostOtherCostList(convertOtherCostList(request.getOtherCostList()));
            projectPlan.setCostTaxCostList(convertTaxCostList(request.getTaxCostList()));

            // 根据产品配方计算直接成本明细（不保存到数据库）
            List<CostDirectCostEntity> directCostList = calculateDirectCostFromRecipe(projectPlan);
            projectPlan.setCostDirectCostList(directCostList);

            // 执行计算（不保存到数据库）
            projectPlan.calculateBudgetBasis();

            // 生成原料明细汇总（按照所有产品型号的底层物料汇总）
            List<MaterialSummaryVO> materialSummaryList = generateMaterialSummaryFromBottomMaterials(projectPlan);

            // 构建响应
            CostProjectPlanCalculateResponse response = new CostProjectPlanCalculateResponse();
            response.setPlanId(null); // 新增时没有ID
            response.setPlanName(projectPlan.getPlanName());
            response.setProjectName(projectPlan.getProjectName());
            response.setDirectCostTotal(projectPlan.getDirectCostTotal());
            response.setOtherCostTotal(projectPlan.getOtherCostTotal());
            response.setTaxCostTotal(projectPlan.getTaxCostTotal());
            response.setCostTotal(projectPlan.getCostTotal());
            response.setProjectProfit(projectPlan.getProjectProfit());
            response.setProfitMargin(projectPlan.getProfitMargin());

            // 计算年度预算应收总计
            java.math.BigDecimal totalRevenue = java.math.BigDecimal.ZERO;
            if (projectPlan.getCostProjectPlanDetailList() != null) {
                totalRevenue = projectPlan.getCostProjectPlanDetailList().stream()
                    .map(detail -> {
                        java.math.BigDecimal total = java.math.BigDecimal.ZERO;
                        if (detail.getRevenueOil() != null) {
                            total = total.add(detail.getRevenueOil());
                        }
                        if (detail.getRevenueWater() != null) {
                            total = total.add(detail.getRevenueWater());
                        }
                        return total;
                    })
                    .reduce(java.math.BigDecimal.ZERO, java.math.BigDecimal::add);
            }
            response.setTotalRevenue(totalRevenue);

            // 设置合同/预估收入（税后万元） = 年度应收预算（油）合计 + 年度应收预算（水）合计
            response.setContractEstimatedIncomeAfterTax(totalRevenue);

            // 不返回计划明细列表
            // response.setDetailList(null);

            // 转换直接成本明细列表
            if (projectPlan.getCostDirectCostList() != null) {
                response.setDirectCostList(
                    projectPlan.getCostDirectCostList().stream()
                        .map(this::convertToDirectCostDTO)
                        .collect(Collectors.toList())
                );
            }

            // 转换其他成本明细列表
            if (projectPlan.getCostOtherCostList() != null) {
                response.setOtherCostList(
                    projectPlan.getCostOtherCostList().stream()
                        .map(this::convertToOtherCostDTO)
                        .collect(Collectors.toList())
                );
            }

            // 转换税金及附加明细列表
            if (projectPlan.getCostTaxCostList() != null) {
                response.setTaxCostList(
                    projectPlan.getCostTaxCostList().stream()
                        .map(this::convertToTaxCostDTO)
                        .collect(Collectors.toList())
                );
            }

            // 转换原料明细汇总
            if (materialSummaryList != null) {
                response.setMaterialSummaryList(
                    materialSummaryList.stream()
                        .map(this::convertToMaterialSummaryDTO)
                        .collect(Collectors.toList())
                );
            }

            return response;

        } catch (Exception e) {
            log.error("计算项目计划预算依据失败，计划名称: {}", request.getPlanName(), e);
            throw new CdkitCloudException("计算失败：" + e.getMessage());
        }
    }

    /**
     * 计算已保存的项目计划预算依据（只计算不保存）
     *
     * @param planId 计划ID
     * @return 计算结果
     */
    public CostProjectPlanCalculateResponse calculateBudgetBasis(String planId) {
        if (!StringUtils.hasText(planId)) {
            throw new CdkitCloudException("计划ID不能为空");
        }

        try {
            // 查询项目计划详情
            CostProjectPlanEntity calculatedPlan = costProjectPlanRepository.queryByIdWithDetails(planId);
            if (calculatedPlan == null) {
                throw new CdkitCloudException("项目计划不存在，ID: " + planId);
            }

            // 根据产品配方计算直接成本明细（只计算不保存）
            List<CostDirectCostEntity> directCostList = calculateDirectCostFromRecipe(calculatedPlan);
            calculatedPlan.setCostDirectCostList(directCostList);

            // 执行计算（不保存到数据库）
            calculatedPlan.calculateBudgetBasis();

            // 生成原料明细汇总（按照所有产品型号的底层物料汇总）
            List<MaterialSummaryVO> materialSummaryList = generateMaterialSummaryFromBottomMaterials(calculatedPlan);

            // 构建响应
            CostProjectPlanCalculateResponse response = new CostProjectPlanCalculateResponse();
            response.setPlanId(calculatedPlan.getId());
            response.setPlanName(calculatedPlan.getPlanName());
            response.setProjectName(calculatedPlan.getProjectName());
            response.setDirectCostTotal(calculatedPlan.getDirectCostTotal());
            response.setOtherCostTotal(calculatedPlan.getOtherCostTotal());
            response.setTaxCostTotal(calculatedPlan.getTaxCostTotal());
            response.setCostTotal(calculatedPlan.getCostTotal());
            response.setProjectProfit(calculatedPlan.getProjectProfit());
            response.setProfitMargin(calculatedPlan.getProfitMargin());

            // 计算年度预算应收总计
           BigDecimal totalRevenue = BigDecimal.ZERO;
            if (calculatedPlan.getCostProjectPlanDetailList() != null) {
                totalRevenue = calculatedPlan.getCostProjectPlanDetailList().stream()
                    .map(detail -> {
                        java.math.BigDecimal total = java.math.BigDecimal.ZERO;
                        if (detail.getRevenueOil() != null) {
                            total = total.add(detail.getRevenueOil());
                        }
                        if (detail.getRevenueWater() != null) {
                            total = total.add(detail.getRevenueWater());
                        }
                        return total;
                    })
                    .reduce(java.math.BigDecimal.ZERO, java.math.BigDecimal::add);
            }
            response.setTotalRevenue(totalRevenue);

            // 设置合同/预估收入（税后万元） = 年度应收预算（油）合计 + 年度应收预算（水）合计
            response.setContractEstimatedIncomeAfterTax(totalRevenue);

            // 不返回计划明细列表
            // response.setDetailList(null);

            // 转换原料明细汇总
            response.setMaterialSummaryList(
                materialSummaryList.stream()
                    .map(this::convertToMaterialSummaryDTO)
                    .collect(Collectors.toList())
            );

            return response;

        } catch (Exception e) {
            log.error("计算项目计划预算依据失败，planId: {}", planId, e);
            throw new CdkitCloudException("计算失败：" + e.getMessage());
        }
    }

    /**
     * 计算并保存项目计划预算依据
     *
     * @param planId 计划ID
     * @return 计算结果
     */
    @Transactional(rollbackFor = Exception.class)
    public CostProjectPlanCalculateResponse calculateAndSaveBudgetBasis(String planId) {
        if (!StringUtils.hasText(planId)) {
            throw new CdkitCloudException("计划ID不能为空");
        }

        try {
            // 查询项目计划详情
            CostProjectPlanEntity calculatedPlan = costProjectPlanRepository.queryByIdWithDetails(planId);
            if (calculatedPlan == null) {
                throw new CdkitCloudException("项目计划不存在，ID: " + planId);
            }

            // 根据产品配方计算直接成本明细
            List<CostDirectCostEntity> directCostList = calculateDirectCostFromRecipe(calculatedPlan);
            calculatedPlan.setCostDirectCostList(directCostList);

            // 执行计算
            calculatedPlan.calculateBudgetBasis();

            // 保存计算结果
            costProjectPlanRepository.updateMain(
                calculatedPlan,
                calculatedPlan.getCostProjectPlanDetailList(),
                calculatedPlan.getCostDirectCostList(),
                calculatedPlan.getCostOtherCostList(),
                calculatedPlan.getCostTaxCostList(),
                calculatedPlan.getCostMaterialDetailList()
            );

            // 生成原料明细汇总（按照所有产品型号的底层物料汇总）
            List<MaterialSummaryVO> materialSummaryList = generateMaterialSummaryFromBottomMaterials(calculatedPlan);

            // 构建响应
            CostProjectPlanCalculateResponse response = new CostProjectPlanCalculateResponse();
            response.setPlanId(calculatedPlan.getId());
            response.setPlanName(calculatedPlan.getPlanName());
            response.setProjectName(calculatedPlan.getProjectName());
            response.setDirectCostTotal(calculatedPlan.getDirectCostTotal());
            response.setOtherCostTotal(calculatedPlan.getOtherCostTotal());
            response.setTaxCostTotal(calculatedPlan.getTaxCostTotal());
            response.setCostTotal(calculatedPlan.getCostTotal());
            response.setProjectProfit(calculatedPlan.getProjectProfit());
            response.setProfitMargin(calculatedPlan.getProfitMargin());

            // 计算年度预算应收总计
            java.math.BigDecimal totalRevenue = java.math.BigDecimal.ZERO;
            if (calculatedPlan.getCostProjectPlanDetailList() != null) {
                totalRevenue = calculatedPlan.getCostProjectPlanDetailList().stream()
                    .map(detail -> {
                        java.math.BigDecimal total = java.math.BigDecimal.ZERO;
                        if (detail.getRevenueOil() != null) {
                            total = total.add(detail.getRevenueOil());
                        }
                        if (detail.getRevenueWater() != null) {
                            total = total.add(detail.getRevenueWater());
                        }
                        return total;
                    })
                    .reduce(java.math.BigDecimal.ZERO, java.math.BigDecimal::add);
            }
            response.setTotalRevenue(totalRevenue);

            // 设置合同/预估收入（税后万元） = 年度应收预算（油）合计 + 年度应收预算（水）合计
            response.setContractEstimatedIncomeAfterTax(totalRevenue);

            // 不返回计划明细列表
            // response.setDetailList(null);

            if (calculatedPlan.getCostDirectCostList() != null) {
                response.setDirectCostList(
                    calculatedPlan.getCostDirectCostList().stream()
                        .map(this::convertToDirectCostDTO)
                        .collect(Collectors.toList())
                );
            }

            if (calculatedPlan.getCostOtherCostList() != null) {
                response.setOtherCostList(
                    calculatedPlan.getCostOtherCostList().stream()
                        .map(this::convertToOtherCostDTO)
                        .collect(Collectors.toList())
                );
            }

            if (calculatedPlan.getCostTaxCostList() != null) {
                response.setTaxCostList(
                    calculatedPlan.getCostTaxCostList().stream()
                        .map(this::convertToTaxCostDTO)
                        .collect(Collectors.toList())
                );
            }

            // 设置原料明细汇总
            if (materialSummaryList != null) {
                response.setMaterialSummaryList(
                    materialSummaryList.stream()
                        .map(this::convertToMaterialSummaryDTO)
                        .collect(Collectors.toList())
                );
            }

            return response;

        } catch (Exception e) {
            log.error("计算并保存项目计划预算依据失败，planId: {}", planId, e);
            throw new CdkitCloudException("计算并保存失败：" + e.getMessage());
        }
    }

    /**
     * 根据产品配方计算直接成本明细
     *
     * @param projectPlan 项目计划实体
     * @return 直接成本明细列表
     */
    private List<CostDirectCostEntity> calculateDirectCostFromRecipe(CostProjectPlanEntity projectPlan) {
        List<CostDirectCostEntity> directCostList = new ArrayList<>();

        if (projectPlan.getCostProjectPlanDetailList() == null || projectPlan.getCostProjectPlanDetailList().isEmpty()) {
            log.warn("项目计划明细为空，无法计算直接成本");
            return directCostList;
        }

        // 按产品型号分组，计算每个产品的总用量
        Map<String, BigDecimal> productUsageMap = projectPlan.getCostProjectPlanDetailList().stream()
            .filter(detail -> StringUtils.hasText(detail.getProductModel()) && detail.getUsageAmount() != null)
            .collect(Collectors.groupingBy(
                CostProjectPlanDetailEntity::getProductModel,
                Collectors.reducing(BigDecimal.ZERO,
                    CostProjectPlanDetailEntity::getUsageAmount,
                    BigDecimal::add)
            ));

        // 为每个产品型号计算直接成本（按产品汇总，不是按底层物料分别生成）
        for (Map.Entry<String, BigDecimal> entry : productUsageMap.entrySet()) {
            String productModel = entry.getKey();
            BigDecimal totalUsage = entry.getValue();

            try {
                // 获取产品配方信息
                ProductRecipeInfo recipeInfo = productFileApplication.getProductRecipeInfo(productModel);
                if (recipeInfo == null) {
                    log.warn("未找到产品配方信息，产品型号: {}", productModel);
                    continue;
                }

                // 计算该产品的材料成本单价（通过底层物料计算）
                CostDirectCostEntity productDirectCost = calculateProductDirectCost(
                    recipeInfo, productModel, totalUsage, projectPlan.getId());
                if (productDirectCost != null) {
                    directCostList.add(productDirectCost);
                }

            } catch (Exception e) {
                log.error("计算产品配方直接成本失败，产品型号: {}", productModel, e);
            }
        }

        return directCostList;
    }

    /**
     * 计算产品的直接成本（按产品汇总）
     *
     * @param recipeInfo 配方信息
     * @param productModel 产品型号
     * @param totalUsage 产品总用量（所有相同产品的用量总和）
     * @param planId 计划ID
     * @return 产品直接成本实体
     */
    private CostDirectCostEntity calculateProductDirectCost(
            ProductRecipeInfo recipeInfo, String productModel, BigDecimal totalUsage, String planId) {

        if (recipeInfo.getBottomLevelMaterials() == null || recipeInfo.getBottomLevelMaterials().isEmpty()) {
            log.warn("配方底层物料为空，配方编号: {}", recipeInfo.getRecipeCode());
            return null;
        }

        // 计算所有底层物料的总成本
        BigDecimal totalMaterialCostIncludingTax = BigDecimal.ZERO;
        BigDecimal totalMaterialCostExcludingTax = BigDecimal.ZERO;
        BigDecimal avgTaxRate = BigDecimal.ZERO;
        int materialCount = 0;

        for (ProductFileTreeEntity material : recipeInfo.getBottomLevelMaterials()) {
            if (!StringUtils.hasText(material.getMaterialCode())) {
                continue;
            }

            try {
                // 查询物料最新单价
                CostMaterialPriceEntity materialPrice = materialPriceApplication.getLatestPriceForEcho(material.getMaterialCode());
                if (materialPrice == null) {
                    log.warn("未找到物料单价信息，物料编码: {}", material.getMaterialCode());
                    continue;
                }

                // 计算该物料的实际用量 = 底层物料计划用量 * 产品总用量
                BigDecimal materialUsage = BigDecimal.ZERO;
                if (material.getPlanNum() != null && totalUsage != null) {
                    materialUsage = material.getPlanNum().multiply(totalUsage);
                }

                // 物料单价
                BigDecimal unitPriceIncludingTax = materialPrice.getTaxUnitPrice() != null ? materialPrice.getTaxUnitPrice() : BigDecimal.ZERO;
                BigDecimal unitPriceExcludingTax = materialPrice.getNonTaxUnitPrice() != null ? materialPrice.getNonTaxUnitPrice() : BigDecimal.ZERO;

                // 计算该物料的总成本 = 物料单价 * 物料用量
                BigDecimal materialTotalIncludingTax = unitPriceIncludingTax.multiply(materialUsage);
                BigDecimal materialTotalExcludingTax = unitPriceExcludingTax.multiply(materialUsage);

                // 累加到总成本
                totalMaterialCostIncludingTax = totalMaterialCostIncludingTax.add(materialTotalIncludingTax);
                totalMaterialCostExcludingTax = totalMaterialCostExcludingTax.add(materialTotalExcludingTax);

                // 累加税率（用于计算平均税率）
                if (materialPrice.getTaxRate() != null) {
                    avgTaxRate = avgTaxRate.add(materialPrice.getTaxRate());
                    materialCount++;
                }

            } catch (Exception e) {
                log.error("计算底层物料成本失败，物料编码: {}", material.getMaterialCode(), e);
            }
        }

        // 计算平均税率
        if (materialCount > 0) {
            avgTaxRate = avgTaxRate.divide(BigDecimal.valueOf(materialCount), 2, RoundingMode.HALF_UP);
        }

        // 计算产品的材料成本单价 = 总材料成本 / 产品总用量
        BigDecimal productUnitPriceIncludingTax = BigDecimal.ZERO;
        BigDecimal productUnitPriceExcludingTax = BigDecimal.ZERO;
        if (totalUsage != null && totalUsage.compareTo(BigDecimal.ZERO) > 0) {
            productUnitPriceIncludingTax = totalMaterialCostIncludingTax.divide(totalUsage, 6, RoundingMode.HALF_UP);
            productUnitPriceExcludingTax = totalMaterialCostExcludingTax.divide(totalUsage, 6, RoundingMode.HALF_UP);
        }

        // 创建产品直接成本记录
        CostDirectCostEntity directCost = new CostDirectCostEntity();
        directCost.setPlanId(planId);
        directCost.setProductName(productModel); // 产品型号
        directCost.setEstimatedUsage(totalUsage); // 预算用量（所有相同产品型号的用量和）
        directCost.setFormulaName(recipeInfo.getRecipeName()); // 配方名称
        directCost.setFormulaCode(recipeInfo.getRecipeCode()); // 配方编码
        directCost.setUnitPriceIncludingTax(productUnitPriceIncludingTax); // 材料成本含税单价
        directCost.setUnitPriceExcludingTax(productUnitPriceExcludingTax); // 材料成本不含税单价
        directCost.setTaxRate(avgTaxRate); // 平均税率
        directCost.setTotalIncludingTax(totalMaterialCostIncludingTax.setScale(2, RoundingMode.HALF_UP)); // 含税总价
        directCost.setTotalExcludingTax(totalMaterialCostExcludingTax.setScale(2, RoundingMode.HALF_UP)); // 不含税总价

        return directCost;
    }



    /**
     * 生成原料明细汇总（按照所有产品型号的底层物料汇总）
     *
     * @param projectPlan 项目计划实体
     * @return 原料明细汇总列表
     */
    private List<MaterialSummaryVO> generateMaterialSummaryFromBottomMaterials(CostProjectPlanEntity projectPlan) {
        List<MaterialSummaryVO> materialSummaryList = new ArrayList<>();

        if (projectPlan.getCostProjectPlanDetailList() == null || projectPlan.getCostProjectPlanDetailList().isEmpty()) {
            return materialSummaryList;
        }

        // 按产品型号分组，计算每个产品的总用量
        Map<String, BigDecimal> productUsageMap = projectPlan.getCostProjectPlanDetailList().stream()
            .filter(detail -> StringUtils.hasText(detail.getProductModel()) && detail.getUsageAmount() != null)
            .collect(Collectors.groupingBy(
                CostProjectPlanDetailEntity::getProductModel,
                Collectors.reducing(BigDecimal.ZERO,
                    CostProjectPlanDetailEntity::getUsageAmount,
                    BigDecimal::add)
            ));

        // 用于汇总所有底层物料
        Map<String, MaterialSummaryVO> materialSummaryMap = new HashMap<>();

        // 遍历每个产品型号
        for (Map.Entry<String, BigDecimal> entry : productUsageMap.entrySet()) {
            String productModel = entry.getKey();
            BigDecimal totalUsage = entry.getValue();

            try {
                // 获取产品配方信息
                ProductRecipeInfo recipeInfo = productFileApplication.getProductRecipeInfo(productModel);
                if (recipeInfo == null) {
                    log.warn("未找到产品配方信息，产品型号: {}", productModel);
                    continue;
                }

                // 处理该产品的底层物料
                if (recipeInfo.getBottomLevelMaterials() != null) {
                    log.info("产品型号: {}, 底层物料数量: {}", productModel, recipeInfo.getBottomLevelMaterials().size());
                    for (ProductFileTreeEntity material : recipeInfo.getBottomLevelMaterials()) {
                        log.info("发现底层物料: {}, 物料名称: {}", material.getMaterialCode(), material.getMaterialName());
                    }
                    for (ProductFileTreeEntity material : recipeInfo.getBottomLevelMaterials()) {
                        if (!StringUtils.hasText(material.getMaterialCode())) {
                            continue;
                        }

                        try {
                            // 查询物料最新单价
                            CostMaterialPriceEntity materialPrice = materialPriceApplication.getLatestPriceForEcho(material.getMaterialCode());
                            if (materialPrice == null) {
                                log.warn("未找到物料单价信息，物料编码: {}，将使用默认单价0", material.getMaterialCode());
                                // 创建默认的物料价格对象，单价为0
                                materialPrice = new CostMaterialPriceEntity();
                                materialPrice.setTaxUnitPrice(BigDecimal.ZERO);
                                materialPrice.setNonTaxUnitPrice(BigDecimal.ZERO);
                                materialPrice.setTaxRate(BigDecimal.ZERO);
                            }

                            // 计算该物料的实际用量 = 底层物料计划用量 * 产品总用量
                            BigDecimal materialUsage = BigDecimal.ZERO;
                            if (material.getPlanNum() != null && totalUsage != null) {
                                materialUsage = material.getPlanNum().multiply(totalUsage);
                            }

                            // 添加调试日志
                            log.debug("处理物料: {}, 计划用量: {}, 产品总用量: {}, 计算用量: {}",
                                material.getMaterialCode(),
                                material.getPlanNum(),
                                totalUsage,
                                materialUsage);

                            // 物料单价和税率（从材料单价管理里根据物料编码查询）
                            BigDecimal unitPriceIncludingTax = materialPrice.getTaxUnitPrice() != null ? materialPrice.getTaxUnitPrice() : BigDecimal.ZERO;
                            BigDecimal unitPriceExcludingTax = materialPrice.getNonTaxUnitPrice() != null ? materialPrice.getNonTaxUnitPrice() : BigDecimal.ZERO;
                            BigDecimal taxRate = materialPrice.getTaxRate() != null ? materialPrice.getTaxRate() : BigDecimal.ZERO;

                            // 计算该物料的总价 = 物料单价(元) * 物料用量 ÷ 10000 (转换为万元)
                            BigDecimal totalPriceIncludingTax = unitPriceIncludingTax.multiply(materialUsage)
                                .divide(new BigDecimal("10000"), 2, RoundingMode.HALF_UP);
                            BigDecimal totalPriceExcludingTax = unitPriceExcludingTax.multiply(materialUsage)
                                .divide(new BigDecimal("10000"), 2, RoundingMode.HALF_UP);

                            // 汇总到材料汇总Map中
                            String materialCode = material.getMaterialCode();
                            if (materialSummaryMap.containsKey(materialCode)) {
                                // 如果已存在，累加用量和总价
                                MaterialSummaryVO existing = materialSummaryMap.get(materialCode);
                                existing.setUsage(existing.getUsage().add(materialUsage));
                                existing.setTotalPriceIncludingTax(existing.getTotalPriceIncludingTax().add(totalPriceIncludingTax));
                                existing.setTotalPriceExcludingTax(existing.getTotalPriceExcludingTax().add(totalPriceExcludingTax));
                                // 重新计算平均单价
                                if (existing.getUsage().compareTo(BigDecimal.ZERO) > 0) {
                                    existing.setUnitPriceIncludingTax(existing.getTotalPriceIncludingTax().divide(existing.getUsage(), 4, RoundingMode.HALF_UP));
                                    existing.setUnitPriceExcludingTax(existing.getTotalPriceExcludingTax().divide(existing.getUsage(), 4, RoundingMode.HALF_UP));
                                }
                                // 税率保持不变（假设同一物料税率相同）
                            } else {
                                // 新增材料汇总记录
                                // 单价也需要转换为万元单位
                                BigDecimal unitPriceIncludingTaxWan = unitPriceIncludingTax.divide(new BigDecimal("10000"), 4, RoundingMode.HALF_UP);
                                BigDecimal unitPriceExcludingTaxWan = unitPriceExcludingTax.divide(new BigDecimal("10000"), 4, RoundingMode.HALF_UP);

                                MaterialSummaryVO summary = MaterialSummaryVO.builder()
                                    .materialCode(materialCode)
                                    .materialName(material.getMaterialName())
                                    .usage(materialUsage)
                                    .unit("吨") // 默认单位
                                    .taxRate(taxRate)
                                    .unitPriceIncludingTax(unitPriceIncludingTaxWan)
                                    .unitPriceExcludingTax(unitPriceExcludingTaxWan)
                                    .totalPriceIncludingTax(totalPriceIncludingTax)
                                    .totalPriceExcludingTax(totalPriceExcludingTax)
                                    .build();
                                materialSummaryMap.put(materialCode, summary);
                            }

                        } catch (Exception e) {
                            log.error("处理底层物料汇总失败，物料编码: {}", material.getMaterialCode(), e);

                            // 即使发生异常，也尝试添加物料到汇总中（使用默认值）
                            try {
                                String materialCode = material.getMaterialCode();
                                if (!materialSummaryMap.containsKey(materialCode)) {
                                    // 新增材料汇总记录（使用默认值）
                                    MaterialSummaryVO summary = MaterialSummaryVO.builder()
                                        .materialCode(materialCode)
                                        .materialName(material.getMaterialName() != null ? material.getMaterialName() : "未知物料")
                                        .usage(BigDecimal.ZERO)
                                        .unit("吨") // 默认单位
                                        .taxRate(BigDecimal.ZERO)
                                        .unitPriceIncludingTax(BigDecimal.ZERO)
                                        .unitPriceExcludingTax(BigDecimal.ZERO)
                                        .totalPriceIncludingTax(BigDecimal.ZERO)
                                        .totalPriceExcludingTax(BigDecimal.ZERO)
                                        .build();
                                    materialSummaryMap.put(materialCode, summary);
                                    log.info("异常处理后添加物料到汇总: {}", materialCode);
                                }
                            } catch (Exception ex) {
                                log.error("尝试在异常处理中添加物料失败", ex);
                            }
                        }
                    }
                }

            } catch (Exception e) {
                log.error("处理产品配方失败，产品型号: {}", productModel, e);
            }
        }

        // 转换为列表并返回
        materialSummaryList.addAll(materialSummaryMap.values());
        return materialSummaryList;
    }

    /**
     * 验证项目计划数据
     */
    public List<String> validateProjectPlanData(String planId) {
        if (!StringUtils.hasText(planId)) {
            throw new CdkitCloudException("计划ID不能为空");
        }
        
        CostProjectPlanEntity projectPlan = costProjectPlanRepository.queryByIdWithDetails(planId);
        if (projectPlan == null) {
            throw new CdkitCloudException("项目计划不存在");
        }
        
        return costProjectPlanDomainService.validateProjectPlanData(projectPlan);
    }

    /**
     * 检查项目计划是否可以提交审批
     */
    public boolean canSubmitForApproval(String planId) {
        if (!StringUtils.hasText(planId)) {
            return false;
        }

        CostProjectPlanEntity projectPlan = costProjectPlanRepository.queryByIdWithDetails(planId);
        if (projectPlan == null) {
            return false;
        }

        return costProjectPlanDomainService.canSubmitForApproval(projectPlan);
    }

    /**
     * 提交项目计划审批
     *
     * @param planId 项目计划ID
     * @param request HTTP请求对象
     * @return 工作流实例ID
     */
    @Transactional(rollbackFor = Exception.class)
    public String submitForApproval(String planId, HttpServletRequest request) {
        log.info("开始提交项目计划审批，planId: {}", planId);

        // 参数校验
        if (!StringUtils.hasText(planId)) {
            throw new CdkitCloudException("项目计划ID不能为空");
        }

        // 查询项目计划
        CostProjectPlanEntity projectPlan = costProjectPlanRepository.queryByIdWithDetails(planId);
        if (projectPlan == null) {
            throw new CdkitCloudException("项目计划不存在");
        }

        // 检查是否可以提交审批
//        if (!costProjectPlanDomainService.canSubmitForApproval(projectPlan)) {
//            throw new CdkitCloudException("项目计划当前状态不允许提交审批，请检查计划数据是否完整");
//        }

        try {
            // 获取工作流进程列表
            List<WorkflowProcessEntity> processEntities = workFlowUtil.getMyStartProcess(request);

            // 查找项目计划审批流程
            WorkflowProcessEntity entity = processEntities.stream()
                    .filter(it -> "cost-项目计划".equals(it.getAppName()))
                    .findFirst()
                    .orElse(null);


            if (entity == null) {
                throw new CdkitCloudException("未找到项目计划审批流程配置");
            }

            // 启动工作流
            String wiid = workFlowUtil.startWorkflow(
                    entity.getAppId(),
                    projectPlan.getId(),
                    "cost-项目计划",
                    "项目计划提交审批：" + projectPlan.getPlanName(),
                    request
            );

            // 更新项目计划状态和工作流实例ID
            projectPlan.setProjectPlanStatus("APPROVING");
            projectPlan.setWiid(wiid);

            // 保存更新
            CostProjectPlanEntity costProjectPlanEntity = costProjectPlanRepository.updateDomainById(projectPlan);

            log.info("项目计划提交审批成功，planId: {}, wiid: {}", planId, wiid);
            return wiid;

        } catch (Exception e) {
            log.error("提交项目计划审批失败，planId: {}", planId, e);
            throw new CdkitCloudException("提交审批失败：" + e.getMessage());
        }
    }

    /**
     * 验证项目计划请求
     */
    private void validateProjectPlanRequest(Object request) {
        if (request == null) {
            throw new CdkitCloudException("请求参数不能为空");
        }
        // 这里可以添加更多的验证逻辑
    }

    /**
     * 转换为领域实体
     */
    private CostProjectPlanEntity convertToEntity(Object request) {
        CostProjectPlanEntity entity = new CostProjectPlanEntity();
        BeanUtils.copyProperties(request, entity);
        return entity;
    }

    /**
     * 转换计算请求为领域实体
     */
    private CostProjectPlanEntity convertCalculateRequestToEntity(CostProjectPlanCalculateRequest request) {
        CostProjectPlanEntity entity = new CostProjectPlanEntity();
        BeanUtils.copyProperties(request, entity);
        return entity;
    }

    /**
     * 转换明细列表
     */
    private List<CostProjectPlanDetailEntity> convertDetailList(List<CostProjectPlanDetailDTO> dtoList) {
        if (dtoList == null) {
            return null;
        }
        return dtoList.stream()
            .map(dto -> {
                CostProjectPlanDetailEntity entity = new CostProjectPlanDetailEntity();
                BeanUtils.copyProperties(dto, entity);
                return entity;
            })
            .collect(Collectors.toList());
    }

    /**
     * 转换直接成本列表
     */
    private List<CostDirectCostEntity> convertDirectCostList(List<CostDirectCostDTO> dtoList) {
        if (dtoList == null) {
            return null;
        }
        return dtoList.stream()
            .map(dto -> {
                CostDirectCostEntity entity = new CostDirectCostEntity();
                BeanUtils.copyProperties(dto, entity);
                return entity;
            })
            .collect(Collectors.toList());
    }

    /**
     * 转换其他成本列表
     */
    private List<CostOtherCostEntity> convertOtherCostList(List<CostOtherCostDTO> dtoList) {
        if (dtoList == null) {
            return null;
        }
        return dtoList.stream()
            .map(dto -> {
                CostOtherCostEntity entity = new CostOtherCostEntity();
                BeanUtils.copyProperties(dto, entity);
                return entity;
            })
            .collect(Collectors.toList());
    }

    /**
     * 转换税金及附加列表
     */
    private List<CostTaxCostEntity> convertTaxCostList(List<CostTaxCostDTO> dtoList) {
        if (dtoList == null) {
            return null;
        }
        return dtoList.stream()
            .map(dto -> {
                CostTaxCostEntity entity = new CostTaxCostEntity();
                BeanUtils.copyProperties(dto, entity);
                return entity;
            })
            .collect(Collectors.toList());
    }

    /**
     * 转换原料明细列表
     */
    private List<CostMaterialDetailEntity> convertMaterialDetailList(List<CostMaterialDetailDTO> dtoList) {
        if (dtoList == null) {
            return null;
        }
        return dtoList.stream()
            .map(dto -> {
                CostMaterialDetailEntity entity = new CostMaterialDetailEntity();
                BeanUtils.copyProperties(dto, entity);
                // 初始化默认值
                entity.initDefaults();
                // 计算总价
                entity.calculateTotalPrice();
                return entity;
            })
            .collect(Collectors.toList());
    }

    /**
     * 转换为DTO
     */
    private CostProjectPlanDTO convertToDTO(CostProjectPlanEntity entity) {
        CostProjectPlanDTO dto = new CostProjectPlanDTO();
        BeanUtils.copyProperties(entity, dto);

        // 转换子表数据
        if (entity.getCostProjectPlanDetailList() != null) {
            dto.setDetailList(
                entity.getCostProjectPlanDetailList().stream()
                    .map(this::convertDetailToDTO)
                    .collect(Collectors.toList())
            );
        }

        if (entity.getCostDirectCostList() != null) {
            dto.setDirectCostList(
                entity.getCostDirectCostList().stream()
                    .map(this::convertDirectCostToDTO)
                    .collect(Collectors.toList())
            );
        }

        if (entity.getCostOtherCostList() != null) {
            dto.setOtherCostList(
                entity.getCostOtherCostList().stream()
                    .map(this::convertOtherCostToDTO)
                    .collect(Collectors.toList())
            );
        }

        if (entity.getCostTaxCostList() != null) {
            dto.setTaxCostList(
                entity.getCostTaxCostList().stream()
                    .map(this::convertTaxCostToDTO)
                    .collect(Collectors.toList())
            );
        }

        if (entity.getCostMaterialDetailList() != null) {
            dto.setMaterialDetailList(
                entity.getCostMaterialDetailList().stream()
                    .map(this::convertMaterialDetailToDTO)
                    .collect(Collectors.toList())
            );
        }

        return dto;
    }

    /**
     * 转换明细为DTO
     */
    private CostProjectPlanCalculateResponse.DetailDTO convertToDetailDTO(CostProjectPlanDetailEntity entity) {
        CostProjectPlanCalculateResponse.DetailDTO dto = new CostProjectPlanCalculateResponse.DetailDTO();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }

    /**
     * 转换直接成本为DTO
     */
    private CostProjectPlanCalculateResponse.DirectCostDTO convertToDirectCostDTO(CostDirectCostEntity entity) {
        CostProjectPlanCalculateResponse.DirectCostDTO dto = new CostProjectPlanCalculateResponse.DirectCostDTO();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }

    /**
     * 转换其他成本为DTO
     */
    private CostProjectPlanCalculateResponse.OtherCostDTO convertToOtherCostDTO(CostOtherCostEntity entity) {
        CostProjectPlanCalculateResponse.OtherCostDTO dto = new CostProjectPlanCalculateResponse.OtherCostDTO();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }

    /**
     * 转换税金及附加为DTO
     */
    private CostProjectPlanCalculateResponse.TaxCostDTO convertToTaxCostDTO(CostTaxCostEntity entity) {
        CostProjectPlanCalculateResponse.TaxCostDTO dto = new CostProjectPlanCalculateResponse.TaxCostDTO();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }

    /**
     * 转换原料明细汇总为DTO
     */
    private CostProjectPlanCalculateResponse.MaterialSummaryDTO convertToMaterialSummaryDTO(MaterialSummaryVO vo) {
        CostProjectPlanCalculateResponse.MaterialSummaryDTO dto = new CostProjectPlanCalculateResponse.MaterialSummaryDTO();
        BeanUtils.copyProperties(vo, dto);
        return dto;
    }

    /**
     * 转换领域明细实体为DTO
     */
    private CostProjectPlanDetailDTO convertDomainDetailToDTO(CostProjectPlanDetailEntity domainDetail) {
        CostProjectPlanDetailDTO dto = new CostProjectPlanDetailDTO();
        BeanUtils.copyProperties(domainDetail, dto);
        return dto;
    }

    /**
     * 转换原料明细汇总VO为DTO
     */
    private CostProjectPlanCalculateResponse.MaterialSummaryDTO convertMaterialSummaryToDTO(MaterialSummaryVO materialSummaryVO) {
        CostProjectPlanCalculateResponse.MaterialSummaryDTO dto = new CostProjectPlanCalculateResponse.MaterialSummaryDTO();
        dto.setMaterialCode(materialSummaryVO.getMaterialCode());
        dto.setMaterialName(materialSummaryVO.getMaterialName());
        dto.setUsage(materialSummaryVO.getUsage());
        dto.setUnit(materialSummaryVO.getUnit());
        dto.setTaxRate(materialSummaryVO.getTaxRate());
        dto.setUnitPriceIncludingTax(materialSummaryVO.getUnitPriceIncludingTax());
        dto.setUnitPriceExcludingTax(materialSummaryVO.getUnitPriceExcludingTax());
        dto.setTotalPriceIncludingTax(materialSummaryVO.getTotalPriceIncludingTax());
        dto.setTotalPriceExcludingTax(materialSummaryVO.getTotalPriceExcludingTax());
        return dto;
    }

    /**
     * 转换为DTO并关联子计划
     */
    private CostProjectPlanDTO convertToDTOWithChildPlans(CostProjectPlanEntity entity) {
        CostProjectPlanDTO dto = new CostProjectPlanDTO();
        BeanUtils.copyProperties(entity, dto);

        // 查询关联的季度计划（子计划）
        List<CostProjectPlanEntity> childPlanEntities = costProjectPlanRepository.queryByParentPlanId(entity.getId());
        if (childPlanEntities != null && !childPlanEntities.isEmpty()) {
            List<CostProjectPlanDTO> childPlanDTOs = childPlanEntities.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
            dto.setChildPlanList(childPlanDTOs);
        }

        return dto;
    }

    /**
     * 转换项目计划明细实体为DTO
     */
    private CostProjectPlanDetailDTO convertDetailToDTO(CostProjectPlanDetailEntity entity) {
        CostProjectPlanDetailDTO dto = new CostProjectPlanDetailDTO();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }

    /**
     * 转换直接成本实体为DTO
     */
    private CostDirectCostDTO convertDirectCostToDTO(CostDirectCostEntity entity) {
        CostDirectCostDTO dto = new CostDirectCostDTO();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }

    /**
     * 转换其他成本实体为DTO
     */
    private CostOtherCostDTO convertOtherCostToDTO(CostOtherCostEntity entity) {
        CostOtherCostDTO dto = new CostOtherCostDTO();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }

    /**
     * 转换税金及附加实体为DTO
     */
    private CostTaxCostDTO convertTaxCostToDTO(CostTaxCostEntity entity) {
        CostTaxCostDTO dto = new CostTaxCostDTO();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }

    /**
     * 转换原料明细实体为DTO
     */
    private CostMaterialDetailDTO convertMaterialDetailToDTO(CostMaterialDetailEntity entity) {
        CostMaterialDetailDTO dto = new CostMaterialDetailDTO();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }
}
